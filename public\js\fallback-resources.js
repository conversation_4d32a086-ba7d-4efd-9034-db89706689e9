/**
 * 备用资源文件
 * 当CDN资源加载失败时使用的本地备用方案
 */

// 简化版APlayer
window.APlayerFallback = class {
  constructor(options) {
    this.container = options.container
    this.audio = options.audio || []
    this.init()
  }
  
  init() {
    if (!this.container) return
    
    this.container.innerHTML = `
      <div style="
        background: #fff;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        padding: 16px;
        margin: 16px 0;
        border: 1px solid #e1e5e9;
      ">
        <div style="color: #666; font-size: 14px; text-align: center;">
          🎵 音乐播放器加载中...
        </div>
        <div style="color: #999; font-size: 12px; text-align: center; margin-top: 8px;">
          CDN资源加载失败，请刷新页面重试
        </div>
      </div>
    `
  }
  
  play() { /* 静默处理 */ }
  pause() { /* 静默处理 */ }
  destroy() { /* 静默处理 */ }
}

// 简化版Live2D
window.Live2DFallback = {
  init() {
    const container = document.getElementById('live2d-widget')
    if (container) {
      container.innerHTML = `
        <div style="
          position: fixed;
          bottom: 20px;
          left: 20px;
          width: 200px;
          height: 200px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 14px;
          text-align: center;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          cursor: pointer;
          z-index: 999;
        ">
          <div>
            <div style="font-size: 24px; margin-bottom: 8px;">🤖</div>
            <div>Live2D<br>加载中...</div>
          </div>
        </div>
      `
    }
  }
}

// 简化版Twikoo
window.TwikooFallback = {
  init(options) {
    const container = document.getElementById(options.el?.replace('#', ''))
    if (container) {
      container.innerHTML = `
        <div style="
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 24px;
          text-align: center;
          margin: 20px 0;
        ">
          <div style="color: #6c757d; font-size: 16px; margin-bottom: 12px;">
            💬 评论系统加载中...
          </div>
          <div style="color: #adb5bd; font-size: 14px;">
            CDN资源加载失败，请刷新页面重试
          </div>
        </div>
      `
    }
  }
}

// 全局错误处理
window.addEventListener('error', function(e) {
  if (e.filename && e.filename.includes('aplayer')) {
    window.APlayer = window.APlayerFallback
  }

  if (e.filename && e.filename.includes('live2d')) {
    setTimeout(() => {
      if (typeof window.loadlive2d === 'undefined') {
        window.Live2DFallback.init()
      }
    }, 1000)
  }

  if (e.filename && e.filename.includes('twikoo')) {
    window.twikoo = window.TwikooFallback
  }
})

// 检查资源加载状态
setTimeout(() => {
  // 检查APlayer
  if (typeof window.APlayer === 'undefined') {
    window.APlayer = window.APlayerFallback
  }

  // 检查Live2D
  if (typeof window.loadlive2d === 'undefined') {
    window.Live2DFallback.init()
  }

  // 检查Twikoo
  if (typeof window.twikoo === 'undefined') {
    window.twikoo = window.TwikooFallback
  }
}, 15000) // 15秒后检查
