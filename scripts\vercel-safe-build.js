#!/usr/bin/env node

/**
 * Vercel安全构建脚本
 * 专门处理网络连接问题，使用静态数据进行构建
 */

const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 Vercel安全构建开始...')

/**
 * 设置安全构建环境
 */
function setupSafeBuildEnv() {
  console.log('⚙️ 设置安全构建环境...')
  
  // 设置环境变量以跳过网络请求
  process.env.NEXT_TELEMETRY_DISABLED = '1'
  process.env.SKIP_ENV_VALIDATION = '1'
  process.env.NODE_ENV = 'production'
  process.env.VERCEL = '1'
  process.env.SKIP_NETWORK_REQUESTS = 'true'
  process.env.BUILD_OFFLINE = 'true'
  process.env.FORCE_STATIC_BUILD = 'true'
  
  console.log('✅ 安全构建环境设置完成')
}

/**
 * 创建静态数据文件
 */
function createStaticData() {
  console.log('📄 创建静态数据文件...')
  
  const staticData = {
    siteInfo: {
      title: 'NotionNext',
      description: 'A blog built with NotionNext',
      pageCover: '',
      icon: ''
    },
    allPages: [
      {
        id: 'home',
        title: '欢迎访问',
        summary: '站点正在构建中，请稍后访问',
        type: 'Post',
        status: 'Published',
        date: new Date().toISOString(),
        slug: 'welcome',
        tags: [],
        category: []
      }
    ],
    categoryOptions: [],
    tagOptions: [],
    notice: null,
    NOTION_CONFIG: {}
  }
  
  const dataDir = path.join(process.cwd(), 'data')
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true })
  }
  
  fs.writeFileSync(
    path.join(dataDir, 'static-site-data.json'),
    JSON.stringify(staticData, null, 2)
  )
  
  console.log('✅ 静态数据文件创建完成')
}

/**
 * 修改Next.js配置以使用静态数据
 */
function patchNextConfig() {
  console.log('🔧 修补Next.js配置...')
  
  const nextConfigPath = path.join(process.cwd(), 'next.config.js')
  if (fs.existsSync(nextConfigPath)) {
    let content = fs.readFileSync(nextConfigPath, 'utf8')
    
    // 添加静态导出配置
    if (!content.includes('FORCE_STATIC_BUILD')) {
      const patchCode = `
// Vercel安全构建补丁
if (process.env.FORCE_STATIC_BUILD === 'true') {
  console.log('🔒 启用静态构建模式')
  module.exports.output = 'export'
  module.exports.trailingSlash = true
  module.exports.images = { unoptimized: true }
  module.exports.i18n = undefined // 禁用i18n以支持静态导出
}
`
      content += patchCode
      fs.writeFileSync(nextConfigPath, content)
      console.log('✅ Next.js配置已修补')
    }
  }
}

/**
 * 运行安全构建
 */
function runSafeBuild() {
  return new Promise((resolve) => {
    console.log('📦 执行安全构建...')
    
    const buildProcess = spawn('npx', ['next', 'build'], {
      stdio: 'pipe',
      shell: true,
      cwd: process.cwd(),
      env: {
        ...process.env,
        FORCE_COLOR: '1'
      }
    })

    let output = ''
    let errorOutput = ''

    buildProcess.stdout.on('data', (data) => {
      const text = data.toString()
      output += text
      process.stdout.write(text)
    })

    buildProcess.stderr.on('data', (data) => {
      const text = data.toString()
      errorOutput += text
      process.stderr.write(text)
    })

    // 15分钟超时
    const timeout = setTimeout(() => {
      console.log('⏰ 构建超时，终止进程...')
      buildProcess.kill('SIGTERM')
      resolve({
        success: false,
        output: output + errorOutput + '\n构建超时'
      })
    }, 900000)

    buildProcess.on('close', (code) => {
      clearTimeout(timeout)
      const fullOutput = output + errorOutput
      
      resolve({
        success: code === 0,
        output: fullOutput
      })
    })

    buildProcess.on('error', (error) => {
      clearTimeout(timeout)
      console.error('构建进程错误:', error)
      resolve({
        success: false,
        output: output + errorOutput + `\n进程错误: ${error.message}`
      })
    })
  })
}

/**
 * 清理临时文件
 */
function cleanup() {
  console.log('🧹 清理临时文件...')
  
  try {
    const dataDir = path.join(process.cwd(), 'data')
    if (fs.existsSync(dataDir)) {
      fs.rmSync(dataDir, { recursive: true, force: true })
    }
    console.log('✅ 清理完成')
  } catch (error) {
    console.warn('清理失败:', error.message)
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    // 设置环境
    setupSafeBuildEnv()
    
    // 创建静态数据
    createStaticData()
    
    // 修补配置
    patchNextConfig()
    
    // 执行构建
    const result = await runSafeBuild()
    
    if (result.success) {
      console.log('✅ Vercel安全构建成功！')
      
      // 生成构建报告
      const report = {
        success: true,
        mode: 'safe-build',
        timestamp: new Date().toISOString(),
        platform: 'Vercel',
        message: 'Vercel安全构建成功完成'
      }
      
      try {
        fs.writeFileSync(
          path.join(process.cwd(), 'vercel-safe-build-report.json'),
          JSON.stringify(report, null, 2)
        )
      } catch (e) {
        console.warn('无法写入构建报告:', e.message)
      }
      
      process.exit(0)
    } else {
      console.log('❌ Vercel安全构建失败')
      
      // 生成失败报告
      const report = {
        success: false,
        mode: 'safe-build',
        timestamp: new Date().toISOString(),
        platform: 'Vercel',
        lastError: result.output,
        suggestions: [
          '检查代码语法错误',
          '验证依赖包安装',
          '查看详细错误日志',
          '尝试本地构建测试'
        ]
      }
      
      try {
        fs.writeFileSync(
          path.join(process.cwd(), 'vercel-safe-build-report.json'),
          JSON.stringify(report, null, 2)
        )
      } catch (e) {
        console.warn('无法写入构建报告:', e.message)
      }
      
      process.exit(1)
    }
    
  } catch (error) {
    console.error('安全构建脚本执行失败:', error)
    process.exit(1)
  } finally {
    // 清理临时文件
    cleanup()
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
  cleanup()
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason)
  cleanup()
  process.exit(1)
})

// 运行主函数
main()
