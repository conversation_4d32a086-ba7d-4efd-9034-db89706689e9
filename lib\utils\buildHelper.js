/**
 * 构建助手工具
 * 专门处理构建时的网络问题和数据获取
 */

/**
 * 检查是否在构建环境中
 * @returns {boolean}
 */
export function isBuildTime() {
  return process.env.NODE_ENV === 'production' && (
    process.env.VERCEL ||
    process.env.CF_PAGES ||
    process.env.NETLIFY ||
    process.env.GITHUB_ACTIONS
  )
}

/**
 * 检查是否应该跳过网络请求
 * @returns {boolean}
 */
export function shouldSkipNetworkRequests() {
  return (
    process.env.SKIP_NETWORK_REQUESTS === 'true' ||
    process.env.BUILD_OFFLINE === 'true' ||
    (isBuildTime() && process.env.FORCE_NETWORK !== 'true')
  )
}

/**
 * 创建默认的站点数据
 * 当网络请求失败时使用
 */
export function createDefaultSiteData() {
  return {
    allPages: [],
    categoryOptions: [],
    tagOptions: [],
    siteInfo: {
      title: 'NotionNext',
      description: 'A blog built with NotionNext',
      pageCover: '',
      icon: ''
    },
    notice: null,
    NOTION_CONFIG: {}
  }
}

/**
 * 创建默认的文章数据
 * 当网络请求失败时使用
 */
export function createDefaultPostData() {
  return {
    id: 'default',
    title: '站点维护中',
    summary: '站点正在维护中，请稍后访问',
    content: '站点正在维护中，请稍后访问',
    type: 'Post',
    status: 'Published',
    date: new Date().toISOString(),
    tags: [],
    category: [],
    slug: 'maintenance',
    password: '',
    blockMap: {}
  }
}

/**
 * 安全的网络请求包装器
 * @param {Function} requestFn - 请求函数
 * @param {*} fallbackData - 失败时的备用数据
 * @param {Object} options - 选项
 * @returns {Promise<*>}
 */
export async function safeNetworkRequest(requestFn, fallbackData, options = {}) {
  const {
    timeout = 30000,
    retries = 3,
    skipOnBuild = true
  } = options

  // 如果在构建时且设置了跳过网络请求
  if (skipOnBuild && shouldSkipNetworkRequests()) {
    console.log('🚫 跳过网络请求（构建模式）')
    return fallbackData
  }

  let lastError = null

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      console.log(`🌐 网络请求尝试 ${attempt}/${retries}`)
      
      // 设置超时
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), timeout)
      })

      const result = await Promise.race([
        requestFn(),
        timeoutPromise
      ])

      console.log('✅ 网络请求成功')
      return result

    } catch (error) {
      lastError = error
      console.warn(`❌ 网络请求失败 (尝试 ${attempt}/${retries}):`, error.message)

      // 如果是最后一次尝试，或者在构建环境中，直接返回备用数据
      if (attempt === retries || isBuildTime()) {
        console.log('🔄 使用备用数据')
        return fallbackData
      }

      // 等待后重试
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000)
      console.log(`⏳ 等待 ${delay}ms 后重试...`)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  console.log('🔄 所有重试失败，使用备用数据')
  return fallbackData
}

/**
 * 创建构建时的环境变量
 */
export function setBuildEnvironment() {
  // 设置构建时的环境变量
  if (isBuildTime()) {
    process.env.SKIP_NETWORK_REQUESTS = 'true'
    process.env.BUILD_OFFLINE = 'true'
    process.env.NEXT_TELEMETRY_DISABLED = '1'
    
    console.log('🏗️ 构建环境已设置')
  }
}

/**
 * 检查网络连接状态
 * @returns {Promise<boolean>}
 */
export async function checkNetworkConnection() {
  try {
    // 尝试连接到一个可靠的服务
    const response = await fetch('https://www.google.com/favicon.ico', {
      method: 'HEAD',
      timeout: 5000
    })
    return response.ok
  } catch (error) {
    console.warn('网络连接检查失败:', error.message)
    return false
  }
}

/**
 * 获取构建状态信息
 */
export function getBuildInfo() {
  return {
    isVercel: !!process.env.VERCEL,
    isCloudflare: !!process.env.CF_PAGES,
    isNetlify: !!process.env.NETLIFY,
    isGitHubActions: !!process.env.GITHUB_ACTIONS,
    isBuildTime: isBuildTime(),
    shouldSkipNetwork: shouldSkipNetworkRequests(),
    nodeEnv: process.env.NODE_ENV,
    timestamp: new Date().toISOString()
  }
}

/**
 * 记录构建信息
 */
export function logBuildInfo() {
  const buildInfo = getBuildInfo()
  console.log('🏗️ 构建信息:', JSON.stringify(buildInfo, null, 2))
}

/**
 * 创建离线模式的数据获取函数
 * @param {Function} onlineDataFn - 在线数据获取函数
 * @param {*} offlineData - 离线数据
 * @returns {Function}
 */
export function createOfflineDataFetcher(onlineDataFn, offlineData) {
  return async (...args) => {
    if (shouldSkipNetworkRequests()) {
      console.log('📱 离线模式：使用静态数据')
      return offlineData
    }

    return safeNetworkRequest(
      () => onlineDataFn(...args),
      offlineData,
      { skipOnBuild: true }
    )
  }
}

/**
 * 处理构建时的错误
 * @param {Error} error - 错误对象
 * @param {string} context - 错误上下文
 * @returns {boolean} 是否应该继续构建
 */
export function handleBuildError(error, context = '') {
  console.error(`🚨 构建错误 ${context}:`, error.message)
  
  // 在构建环境中，网络错误不应该阻止构建
  if (isBuildTime() && isNetworkError(error)) {
    console.log('🔄 网络错误在构建时被忽略，继续构建...')
    return true
  }
  
  return false
}

/**
 * 检查是否是网络错误
 * @param {Error} error - 错误对象
 * @returns {boolean}
 */
function isNetworkError(error) {
  const networkErrorCodes = [
    'ECONNRESET',
    'ETIMEDOUT',
    'ENOTFOUND',
    'ECONNREFUSED',
    'ENETUNREACH'
  ]
  
  return networkErrorCodes.some(code => 
    error.code === code || error.message.includes(code)
  )
}
