/**
 * 网络请求助手
 * 提供重试机制和错误处理，专门解决ECONNRESET等网络问题
 */

/**
 * 带重试的网络请求
 * @param {Function} requestFn - 请求函数
 * @param {Object} options - 配置选项
 * @returns {Promise} 请求结果
 */
export async function retryRequest(requestFn, options = {}) {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    backoffMultiplier = 2,
    timeout = 30000,
    retryCondition = (error) => {
      // 默认重试条件：网络错误、超时、5xx错误
      return (
        error.code === 'ECONNRESET' ||
        error.code === 'ETIMEDOUT' ||
        error.code === 'ENOTFOUND' ||
        error.code === 'ECONNREFUSED' ||
        (error.response && error.response.status >= 500)
      )
    }
  } = options

  let lastError = null
  let currentDelay = retryDelay

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // 设置超时
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), timeout)
      })

      const result = await Promise.race([
        requestFn(),
        timeoutPromise
      ])

      return result
    } catch (error) {
      lastError = error
      
      // 记录错误
      console.warn(`请求失败 (尝试 ${attempt + 1}/${maxRetries + 1}):`, {
        error: error.message,
        code: error.code,
        status: error.response?.status
      })

      // 如果是最后一次尝试，或者不满足重试条件，直接抛出错误
      if (attempt === maxRetries || !retryCondition(error)) {
        throw error
      }

      // 等待后重试
      console.log(`等待 ${currentDelay}ms 后重试...`)
      await new Promise(resolve => setTimeout(resolve, currentDelay))
      currentDelay *= backoffMultiplier
    }
  }

  throw lastError
}

/**
 * 创建带重试的Notion API请求
 * @param {Object} notionAPI - Notion API实例
 * @returns {Object} 包装后的API
 */
export function createRetryNotionAPI(notionAPI) {
  return {
    getPage: async (pageId, options = {}) => {
      return retryRequest(
        () => notionAPI.getPage(pageId),
        {
          maxRetries: 3,
          retryDelay: 2000,
          timeout: 45000,
          ...options
        }
      )
    },

    getBlocks: async (blockId, options = {}) => {
      return retryRequest(
        () => notionAPI.getBlocks(blockId),
        {
          maxRetries: 3,
          retryDelay: 2000,
          timeout: 45000,
          ...options
        }
      )
    },

    getCollection: async (collectionId, collectionViewId, options = {}) => {
      return retryRequest(
        () => notionAPI.getCollection(collectionId, collectionViewId),
        {
          maxRetries: 3,
          retryDelay: 2000,
          timeout: 45000,
          ...options
        }
      )
    }
  }
}

/**
 * 网络状态检查
 * @returns {boolean} 是否有网络连接
 */
export function isNetworkAvailable() {
  if (typeof navigator !== 'undefined' && 'onLine' in navigator) {
    return navigator.onLine
  }
  return true // 服务端默认认为有网络
}

/**
 * 创建带超时的Promise
 * @param {Promise} promise - 原始Promise
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise} 带超时的Promise
 */
export function withTimeout(promise, timeout = 30000) {
  return Promise.race([
    promise,
    new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`Operation timed out after ${timeout}ms`)), timeout)
    })
  ])
}

/**
 * 批量请求处理器
 * @param {Array} requests - 请求数组
 * @param {Object} options - 配置选项
 * @returns {Promise<Array>} 结果数组
 */
export async function batchRequests(requests, options = {}) {
  const {
    concurrency = 3,
    delayBetweenBatches = 1000
  } = options

  const results = []
  
  for (let i = 0; i < requests.length; i += concurrency) {
    const batch = requests.slice(i, i + concurrency)
    
    try {
      const batchResults = await Promise.allSettled(batch)
      results.push(...batchResults)
      
      // 批次间延迟
      if (i + concurrency < requests.length) {
        await new Promise(resolve => setTimeout(resolve, delayBetweenBatches))
      }
    } catch (error) {
      console.error('批量请求失败:', error)
      // 继续处理下一批
    }
  }
  
  return results
}

/**
 * 错误分类器
 * @param {Error} error - 错误对象
 * @returns {string} 错误类型
 */
export function classifyError(error) {
  if (error.code === 'ECONNRESET') return 'connection_reset'
  if (error.code === 'ETIMEDOUT') return 'timeout'
  if (error.code === 'ENOTFOUND') return 'dns_error'
  if (error.code === 'ECONNREFUSED') return 'connection_refused'
  if (error.response?.status >= 500) return 'server_error'
  if (error.response?.status === 429) return 'rate_limit'
  if (error.response?.status >= 400) return 'client_error'
  return 'unknown_error'
}

/**
 * 创建错误恢复策略
 * @param {string} errorType - 错误类型
 * @returns {Object} 恢复策略
 */
export function getRecoveryStrategy(errorType) {
  const strategies = {
    connection_reset: {
      shouldRetry: true,
      maxRetries: 5,
      retryDelay: 2000,
      backoffMultiplier: 1.5
    },
    timeout: {
      shouldRetry: true,
      maxRetries: 3,
      retryDelay: 3000,
      backoffMultiplier: 2
    },
    dns_error: {
      shouldRetry: true,
      maxRetries: 2,
      retryDelay: 5000,
      backoffMultiplier: 1
    },
    rate_limit: {
      shouldRetry: true,
      maxRetries: 3,
      retryDelay: 10000,
      backoffMultiplier: 2
    },
    server_error: {
      shouldRetry: true,
      maxRetries: 3,
      retryDelay: 5000,
      backoffMultiplier: 2
    },
    client_error: {
      shouldRetry: false,
      maxRetries: 0,
      retryDelay: 0,
      backoffMultiplier: 1
    },
    unknown_error: {
      shouldRetry: true,
      maxRetries: 2,
      retryDelay: 3000,
      backoffMultiplier: 1.5
    }
  }

  return strategies[errorType] || strategies.unknown_error
}
