/**
 * 部署平台优化配置
 * 针对 Vercel、Cloudflare Pages、Netlify 等平台的优化设置
 */

const DEPLOYMENT_CONFIG = {
  // 资源优化
  RESOURCE_OPTIMIZATION: {
    // 启用资源预加载
    ENABLE_PRELOAD: process.env.NEXT_PUBLIC_ENABLE_PRELOAD !== 'false',
    
    // 启用资源压缩
    ENABLE_COMPRESSION: process.env.NEXT_PUBLIC_ENABLE_COMPRESSION !== 'false',
    
    // CDN优化
    CDN_OPTIMIZATION: process.env.NEXT_PUBLIC_CDN_OPTIMIZATION !== 'false',
    
    // 图片优化
    IMAGE_OPTIMIZATION: process.env.NEXT_PUBLIC_IMAGE_OPTIMIZATION !== 'false'
  },

  // 错误处理
  ERROR_HANDLING: {
    // 启用全局错误处理
    ENABLE_GLOBAL_ERROR_HANDLER: process.env.NEXT_PUBLIC_ENABLE_GLOBAL_ERROR_HANDLER !== 'false',
    
    // 静默第三方库错误
    SILENCE_THIRD_PARTY_ERRORS: process.env.NEXT_PUBLIC_SILENCE_THIRD_PARTY_ERRORS !== 'false',
    
    // 错误上报
    ENABLE_ERROR_REPORTING: process.env.NEXT_PUBLIC_ENABLE_ERROR_REPORTING === 'true'
  },

  // 性能优化
  PERFORMANCE: {
    // 启用懒加载
    ENABLE_LAZY_LOADING: process.env.NEXT_PUBLIC_ENABLE_LAZY_LOADING !== 'false',
    
    // 启用代码分割
    ENABLE_CODE_SPLITTING: process.env.NEXT_PUBLIC_ENABLE_CODE_SPLITTING !== 'false',
    
    // 预取关键资源
    ENABLE_PREFETCH: process.env.NEXT_PUBLIC_ENABLE_PREFETCH !== 'false'
  },

  // 平台特定配置
  PLATFORM_SPECIFIC: {
    // Vercel 优化
    VERCEL: {
      // 启用 Vercel Analytics
      ENABLE_ANALYTICS: process.env.VERCEL_ANALYTICS_ID ? true : false,
      
      // 启用边缘函数
      ENABLE_EDGE_FUNCTIONS: process.env.NEXT_PUBLIC_ENABLE_EDGE_FUNCTIONS === 'true'
    },

    // Cloudflare Pages 优化
    CLOUDFLARE: {
      // 启用 Cloudflare Analytics
      ENABLE_ANALYTICS: process.env.CF_ANALYTICS_TOKEN ? true : false,
      
      // 启用 Workers
      ENABLE_WORKERS: process.env.NEXT_PUBLIC_ENABLE_CF_WORKERS === 'true'
    },

    // Netlify 优化
    NETLIFY: {
      // 启用 Netlify Analytics
      ENABLE_ANALYTICS: process.env.NETLIFY_ANALYTICS_ID ? true : false,
      
      // 启用 Functions
      ENABLE_FUNCTIONS: process.env.NEXT_PUBLIC_ENABLE_NETLIFY_FUNCTIONS === 'true'
    }
  },

  // 兼容性设置
  COMPATIBILITY: {
    // 支持旧版浏览器
    SUPPORT_LEGACY_BROWSERS: process.env.NEXT_PUBLIC_SUPPORT_LEGACY_BROWSERS === 'true',
    
    // 启用 Polyfills
    ENABLE_POLYFILLS: process.env.NEXT_PUBLIC_ENABLE_POLYFILLS !== 'false',
    
    // 移动端优化
    MOBILE_OPTIMIZATION: process.env.NEXT_PUBLIC_MOBILE_OPTIMIZATION !== 'false'
  },

  // 安全设置
  SECURITY: {
    // 内容安全策略
    ENABLE_CSP: process.env.NEXT_PUBLIC_ENABLE_CSP === 'true',
    
    // HTTPS 重定向
    FORCE_HTTPS: process.env.NEXT_PUBLIC_FORCE_HTTPS !== 'false',
    
    // 安全头部
    SECURITY_HEADERS: process.env.NEXT_PUBLIC_SECURITY_HEADERS !== 'false'
  }
}

module.exports = DEPLOYMENT_CONFIG
