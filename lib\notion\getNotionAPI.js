import { NotionAP<PERSON> as NotionLibrary } from 'notion-client'
import BLOG from '@/blog.config'
import { createRetryNotionAPI } from '@/lib/utils/networkHelper'

const baseNotionAPI = getNotionAPI()
const notionAPI = createRetryNotionAPI(baseNotionAPI)

function getNotionAPI() {
  return new NotionLibrary({
    activeUser: BLOG.NOTION_ACTIVE_USER || null,
    authToken: BLOG.NOTION_TOKEN_V2 || null,
    userTimeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
  })
}

export default notionAPI
