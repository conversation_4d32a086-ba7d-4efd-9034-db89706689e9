/**
 * 紧急修复脚本 - 立即移除音乐列表泄漏
 * 这个脚本会在页面加载的最早阶段执行
 */

// 立即执行的修复函数
(function emergencyFix() {
  'use strict';
  
  // 强制移除所有可能的音乐列表
  function removeAllMusicLists() {
    // 获取所有ol和ul元素
    const allOL = document.getElementsByTagName('ol');
    const allUL = document.getElementsByTagName('ul');
    
    // 转换为数组以避免动态集合问题
    const olArray = Array.from(allOL);
    const ulArray = Array.from(allUL);
    
    // 检查并移除音乐列表
    [...olArray, ...ulArray].forEach(list => {
      const text = list.textContent || '';
      const parent = list.parentElement;
      
      // 检查是否是音乐列表
      const isMusicList = text.includes('若是月亮还没来') || 
                         text.includes('天空没有极限') || 
                         text.includes('星空旅行者') ||
                         text.includes('未末') ||
                         text.includes('海街寺庙') ||
                         text.includes('点燃生命的光') ||
                         text.includes('Fight') ||
                         text.includes('BeatBrothers') ||
                         text.includes('G.E.M') ||
                         text.includes('邓紫棋') ||
                         text.includes('王宇宙Leto') ||
                         text.includes('乔浚丞');
      
      // 检查是否是直接在body或main下的列表
      const isDirectChild = parent && (
        parent.tagName === 'BODY' || 
        parent.tagName === 'MAIN' || 
        parent.id === '__next' ||
        parent.className.includes('container')
      );
      
      // 检查是否是长列表（可能是音乐列表）
      const isLongList = list.children.length > 8;
      
      // 如果满足任一条件，立即移除
      if (isMusicList || (isDirectChild && isLongList)) {
        try {
          list.remove();
        } catch (e) {
          // 如果remove失败，尝试隐藏
          list.style.display = 'none';
          list.style.visibility = 'hidden';
          list.style.opacity = '0';
          list.style.height = '0';
          list.style.overflow = 'hidden';
          list.style.position = 'absolute';
          list.style.left = '-9999px';
          list.style.top = '-9999px';
        }
      }
    });
  }
  
  // 立即执行
  removeAllMusicLists();
  
  // 在不同时机重复执行
  setTimeout(removeAllMusicLists, 1);
  setTimeout(removeAllMusicLists, 10);
  setTimeout(removeAllMusicLists, 50);
  setTimeout(removeAllMusicLists, 100);
  setTimeout(removeAllMusicLists, 200);
  setTimeout(removeAllMusicLists, 500);
  setTimeout(removeAllMusicLists, 1000);
  
  // DOM内容加载完成后执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', removeAllMusicLists);
  }
  
  // 页面完全加载后执行
  if (document.readyState !== 'complete') {
    window.addEventListener('load', removeAllMusicLists);
  }
  
  // 监听DOM变化
  if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
      let hasNewLists = false;
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(function(node) {
            if (node.nodeType === Node.ELEMENT_NODE) {
              if (node.tagName === 'OL' || node.tagName === 'UL') {
                hasNewLists = true;
              } else if (node.querySelectorAll) {
                const lists = node.querySelectorAll('ol, ul');
                if (lists.length > 0) {
                  hasNewLists = true;
                }
              }
            }
          });
        }
      });
      
      if (hasNewLists) {
        setTimeout(removeAllMusicLists, 1);
      }
    });
    
    // 开始观察
    if (document.body) {
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    } else {
      // 如果body还没准备好，等待一下
      setTimeout(function() {
        if (document.body) {
          observer.observe(document.body, {
            childList: true,
            subtree: true
          });
        }
      }, 10);
    }
  }
  
})();

// 添加全局样式来隐藏任何可能的泄漏
(function addEmergencyStyles() {
  const style = document.createElement('style');
  style.textContent = `
    /* 紧急隐藏规则 */
    body > ol:not(.allowed-list),
    body > ul:not(.allowed-list),
    main > ol:not(.allowed-list),
    main > ul:not(.allowed-list),
    #__next > ol:not(.allowed-list),
    #__next > ul:not(.allowed-list) {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
      height: 0 !important;
      width: 0 !important;
      overflow: hidden !important;
      position: absolute !important;
      left: -9999px !important;
      top: -9999px !important;
    }
  `;
  
  // 立即添加样式
  if (document.head) {
    document.head.appendChild(style);
  } else {
    // 如果head还没准备好，等待一下
    setTimeout(function() {
      if (document.head) {
        document.head.appendChild(style);
      }
    }, 1);
  }
})();
