/**
 * 资源优先级加载管理器
 * 按照指定顺序加载资源：桌面视频 → Live2D → APlayer → Chatbase → 其他
 */

import { loadResourceWithFallback } from './resourceLoader'

// 资源加载优先级配置
const RESOURCE_PRIORITY = {
  1: 'desktop-video',    // 桌面背景视频
  2: 'live2d',          // Live2D
  3: 'aplayer',         // 音乐播放器
  4: 'chatbase',        // Chatbase聊天机器人
  5: 'others'           // 其他资源
}

// 资源加载状态
const loadingState = {
  'desktop-video': false,
  'live2d': false,
  'aplayer': false,
  'chatbase': false,
  'others': false
}

// 加载完成回调
const loadingCallbacks = {
  'desktop-video': [],
  'live2d': [],
  'aplayer': [],
  'chatbase': [],
  'others': []
}

/**
 * 加载桌面背景视频
 */
async function loadDesktopVideo() {
  return new Promise((resolve, reject) => {
    console.log('🎬 开始加载桌面背景视频...')
    
    const video = document.createElement('video')
    video.src = '/favtion.mp4'
    video.preload = 'auto'
    video.muted = true
    video.loop = true
    
    video.onloadeddata = () => {
      console.log('✅ 桌面背景视频加载完成')
      loadingState['desktop-video'] = true
      resolve('desktop-video')
    }
    
    video.onerror = (error) => {
      console.warn('⚠️ 桌面背景视频加载失败:', error)
      loadingState['desktop-video'] = true // 标记为完成，继续下一个
      resolve('desktop-video')
    }
    
    // 5秒超时
    setTimeout(() => {
      if (!loadingState['desktop-video']) {
        console.warn('⏰ 桌面背景视频加载超时')
        loadingState['desktop-video'] = true
        resolve('desktop-video')
      }
    }, 5000)
  })
}

/**
 * 加载Live2D资源
 */
async function loadLive2D() {
  console.log('🤖 开始加载Live2D资源...')
  
  try {
    await loadResourceWithFallback('live2d', 'js', 10000)
    console.log('✅ Live2D资源加载完成')
    loadingState['live2d'] = true
    return 'live2d'
  } catch (error) {
    console.warn('⚠️ Live2D资源加载失败:', error.message)
    loadingState['live2d'] = true
    return 'live2d'
  }
}

/**
 * 加载APlayer资源
 */
async function loadAPlayer() {
  console.log('🎵 开始加载APlayer资源...')
  
  try {
    // 先加载CSS
    await loadResourceWithFallback('aplayerCSS', 'css', 8000)
    console.log('✅ APlayer CSS加载完成')
    
    // 再加载JS
    await loadResourceWithFallback('aplayer', 'js', 8000)
    console.log('✅ APlayer JS加载完成')
    
    loadingState['aplayer'] = true
    return 'aplayer'
  } catch (error) {
    console.warn('⚠️ APlayer资源加载失败:', error.message)
    loadingState['aplayer'] = true
    return 'aplayer'
  }
}

/**
 * 加载Chatbase资源
 */
async function loadChatbase() {
  console.log('💬 开始加载Chatbase资源...')
  
  return new Promise((resolve) => {
    // Chatbase通常通过外部脚本加载，这里只是标记
    setTimeout(() => {
      console.log('✅ Chatbase资源加载完成')
      loadingState['chatbase'] = true
      resolve('chatbase')
    }, 1000)
  })
}

/**
 * 加载其他资源
 */
async function loadOthers() {
  console.log('🔧 开始加载其他资源...')
  
  const otherResources = [
    loadResourceWithFallback('prism', 'css', 6000).catch(() => {}),
    loadResourceWithFallback('twikoo', 'js', 6000).catch(() => {}),
    loadResourceWithFallback('meting', 'js', 6000).catch(() => {})
  ]
  
  try {
    await Promise.allSettled(otherResources)
    console.log('✅ 其他资源加载完成')
  } catch (error) {
    console.warn('⚠️ 部分其他资源加载失败')
  }
  
  loadingState['others'] = true
  return 'others'
}

/**
 * 按优先级顺序加载所有资源
 */
export async function loadResourcesByPriority() {
  console.log('🚀 开始按优先级加载资源...')
  
  const loaders = [
    loadDesktopVideo,
    loadLive2D,
    loadAPlayer,
    loadChatbase,
    loadOthers
  ]
  
  for (let i = 0; i < loaders.length; i++) {
    const loader = loaders[i]
    const resourceName = Object.values(RESOURCE_PRIORITY)[i]
    
    try {
      console.log(`📦 加载优先级 ${i + 1}: ${resourceName}`)
      await loader()
      
      // 执行回调
      const callbacks = loadingCallbacks[resourceName] || []
      callbacks.forEach(callback => {
        try {
          callback()
        } catch (error) {
          console.warn(`回调执行失败 (${resourceName}):`, error)
        }
      })
      
      // 短暂延迟，避免阻塞UI
      await new Promise(resolve => setTimeout(resolve, 100))
      
    } catch (error) {
      console.error(`❌ 资源加载失败 (${resourceName}):`, error)
    }
  }
  
  console.log('🎉 所有资源加载完成！')
}

/**
 * 注册资源加载完成回调
 */
export function onResourceLoaded(resourceName, callback) {
  if (loadingState[resourceName]) {
    // 已经加载完成，立即执行
    callback()
  } else {
    // 注册回调
    if (!loadingCallbacks[resourceName]) {
      loadingCallbacks[resourceName] = []
    }
    loadingCallbacks[resourceName].push(callback)
  }
}

/**
 * 检查资源是否已加载
 */
export function isResourceLoaded(resourceName) {
  return loadingState[resourceName] || false
}

/**
 * 获取所有资源加载状态
 */
export function getLoadingState() {
  return { ...loadingState }
}
