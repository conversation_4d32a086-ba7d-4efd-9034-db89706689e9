/**
 * 音乐列表泄漏修复脚本
 * 立即隐藏任何可能泄漏到页面上的音乐列表
 */

(function() {
  'use strict';

  // 强力修复函数 - 立即移除所有泄漏的列表
  function forceRemoveMusicListLeaks() {
    // 更广泛的选择器
    const selectors = [
      'body > ol',
      'body > ul',
      'main > ol',
      'main > ul',
      '#__next > ol',
      '#__next > ul',
      '.container > ol',
      '.container > ul',
      'div > ol:not([class])',
      'div > ul:not([class])'
    ];

    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        // 检查是否是音乐列表或可疑的长列表
        const text = element.textContent || '';
        const hasMusic = text.includes('若是月亮还没来') ||
                        text.includes('天空没有极限') ||
                        text.includes('星空旅行者') ||
                        text.includes('未末') ||
                        text.includes('海街寺庙') ||
                        text.includes('点燃生命的光') ||
                        text.includes('Fight') ||
                        text.includes('BeatBrothers') ||
                        text.includes('G.E.M') ||
                        text.includes('邓紫棋');

        // 如果包含音乐内容或者是长列表，立即移除
        if (hasMusic || element.children.length > 5) {
          element.remove();
        }
      });
    });

    // 额外检查：移除任何包含大量li元素的ol/ul
    const allLists = document.querySelectorAll('ol, ul');
    allLists.forEach(list => {
      if (list.children.length > 10) {
        const text = list.textContent || '';
        if (text.includes('若是月亮还没来') || text.includes('Fight') || text.includes('邓紫棋')) {
          list.remove();
        }
      }
    });
  }

  // 立即执行多次
  forceRemoveMusicListLeaks();
  setTimeout(forceRemoveMusicListLeaks, 10);
  setTimeout(forceRemoveMusicListLeaks, 100);
  setTimeout(forceRemoveMusicListLeaks, 500);

  // DOM加载完成后再次执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', forceRemoveMusicListLeaks);
  } else {
    forceRemoveMusicListLeaks();
  }
  
  // 监听DOM变化
  const observer = new MutationObserver(function(mutations) {
    let shouldCheck = false;
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        for (let node of mutation.addedNodes) {
          if (node.nodeType === Node.ELEMENT_NODE &&
              (node.tagName === 'OL' || node.tagName === 'UL')) {
            shouldCheck = true;
            break;
          }
        }
      }
    });

    if (shouldCheck) {
      setTimeout(forceRemoveMusicListLeaks, 1);
      setTimeout(forceRemoveMusicListLeaks, 10);
    }
  });
  
  // 开始监听
  if (document.body) {
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  } else {
    // 如果body还没准备好，等待一下
    setTimeout(function() {
      if (document.body) {
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      }
    }, 100);
  }
  
  // 页面可见性变化时也检查
  document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
      setTimeout(forceRemoveMusicListLeaks, 10);
      setTimeout(forceRemoveMusicListLeaks, 100);
    }
  });

  // 定期检查（前5秒内每秒检查一次）
  for (let i = 1; i <= 5; i++) {
    setTimeout(forceRemoveMusicListLeaks, i * 1000);
  }
  
})();
