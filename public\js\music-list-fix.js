/**
 * 音乐列表泄漏修复脚本
 * 立即隐藏任何可能泄漏到页面上的音乐列表
 */

(function() {
  'use strict';
  
  // 立即执行的修复函数
  function hideMusicListLeaks() {
    // 查找所有可能的泄漏列表
    const selectors = [
      'body > ol:not([class*="post"]):not([class*="article"]):not([class*="content"])',
      'body > ul:not([class*="post"]):not([class*="article"]):not([class*="content"])',
      'main > ol:not([class*="post"]):not([class*="article"]):not([class*="content"])',
      'main > ul:not([class*="post"]):not([class*="article"]):not([class*="content"])',
      '#__next > ol:not([class*="post"]):not([class*="article"]):not([class*="content"])',
      '#__next > ul:not([class*="post"]):not([class*="article"]):not([class*="content"])'
    ];
    
    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        // 检查是否包含音乐相关内容
        const text = element.textContent || '';
        if (text.includes('若是月亮还没来') || 
            text.includes('天空没有极限') || 
            text.includes('星空旅行者') ||
            text.includes('未末') ||
            text.includes('海街寺庙') ||
            text.includes('点燃生命的光') ||
            text.includes('Fight') ||
            element.children.length > 5) { // 可能是音乐列表
          element.style.display = 'none';
          element.style.visibility = 'hidden';
          element.style.opacity = '0';
          element.style.height = '0';
          element.style.overflow = 'hidden';
          element.remove(); // 直接移除
        }
      });
    });
  }
  
  // 立即执行
  hideMusicListLeaks();
  
  // DOM加载完成后再次执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', hideMusicListLeaks);
  } else {
    hideMusicListLeaks();
  }
  
  // 监听DOM变化
  const observer = new MutationObserver(function(mutations) {
    let shouldCheck = false;
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        for (let node of mutation.addedNodes) {
          if (node.nodeType === Node.ELEMENT_NODE && 
              (node.tagName === 'OL' || node.tagName === 'UL')) {
            shouldCheck = true;
            break;
          }
        }
      }
    });
    
    if (shouldCheck) {
      setTimeout(hideMusicListLeaks, 10);
    }
  });
  
  // 开始监听
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  // 页面可见性变化时也检查
  document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
      setTimeout(hideMusicListLeaks, 100);
    }
  });
  
})();
