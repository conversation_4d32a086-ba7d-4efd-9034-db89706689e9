/* eslint-disable no-undef */
import { siteConfig } from '@/lib/config'
import { useGlobal } from '@/lib/global'
import { isMobile } from '@/lib/utils'
import { loadResourceWithFallback } from '@/lib/utils/resourceLoader'
import { useEffect, useRef } from 'react'

export default function Live2D() {
  const { theme } = useGlobal()
  const showPet = JSON.parse(siteConfig('WIDGET_PET'))
  const petLink = siteConfig('WIDGET_PET_LINK')

  const audioRef = useRef(null)
  const lastPlayedIndexRef = useRef(-1)
  const petLoadedRef = useRef(false)
  const scriptLoadedRef = useRef(false)

  const audioUrls = [
    'https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/music/live2d/tap_Rhand.mp3',
    'https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/music/live2d/tap_body.mp3',
    'https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/music/live2d/tap_Lhand.mp3',
    'https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/music/live2d/start.mp3'
  ]

  useEffect(() => {
    if (showPet && !isMobile() && !petLoadedRef.current) {
      const initLive2D = () => {
        try {
          // 添加延迟确保DOM完全加载
          setTimeout(() => {
            if (typeof window?.loadlive2d !== 'undefined') {
              const canvas = document.getElementById('live2d')
              if (canvas && petLink) {
                console.log('🤖 初始化Live2D...')
                loadlive2d('live2d', petLink)
                petLoadedRef.current = true

                const safeClickHandler = () => {
                  try {
                    playRandomAudio()
                  } catch (e) {
                    console.warn('播放音效失败:', e)
                  }
                }
                canvas.removeEventListener('click', safeClickHandler)
                canvas.addEventListener('click', safeClickHandler)
                console.log('✅ Live2D 初始化成功')
              }
            } else {
              console.warn('⚠️ Live2D 脚本未加载，使用备用方案')
              // 使用备用方案
              if (window.Live2DFallback) {
                window.Live2DFallback.init()
              }
            }
          }, 100)
        } catch (err) {
          console.error('❌ Live2D 初始化失败:', err)
          // 使用备用方案
          if (window.Live2DFallback) {
            window.Live2DFallback.init()
          }
        }
      }

      if (typeof window?.loadlive2d !== 'undefined') {
        initLive2D()
      } else {
        if (!scriptLoadedRef.current) {
          scriptLoadedRef.current = true
          console.log('🔄 加载Live2D脚本...')

          loadResourceWithFallback('live2d', 'js', 12000)
            .then(() => {
              console.log('✅ Live2D脚本加载成功')
              initLive2D()
            })
            .catch(error => {
              console.error('❌ Live2D 脚本加载失败:', error.message)
              scriptLoadedRef.current = false // 重置状态，允许重试

              // 使用备用方案
              console.log('🔄 使用Live2D备用方案')
              if (window.Live2DFallback) {
                window.Live2DFallback.init()
              }
            })
        }
      }
    }

    return () => {
      try {
        const live2dCanvas = document.getElementById('live2d')
        if (live2dCanvas) {
          live2dCanvas.replaceWith(live2dCanvas.cloneNode(true))
        }
      } catch (e) {
        console.warn('Live2D 清理失败:', e)
      }
    }
  }, [theme, petLink, showPet])

  function playRandomAudio() {
    if (!audioRef.current) {
      audioRef.current = new Audio()
      audioRef.current.preload = 'auto'
    }

    if (!audioRef.current.paused) {
      audioRef.current.pause()
      audioRef.current.currentTime = 0
    }

    let randomIndex
    if (audioUrls.length > 1) {
      do {
        randomIndex = Math.floor(Math.random() * audioUrls.length)
      } while (randomIndex === lastPlayedIndexRef.current)
    } else {
      randomIndex = 0
    }

    // 添加音频加载错误处理
    const tryPlayAudio = (url) => {
      audioRef.current.src = url
      audioRef.current.volume = 1.0
      lastPlayedIndexRef.current = randomIndex

      audioRef.current.onerror = () => {
        console.warn('音频加载失败:', url)
        // 尝试备用CDN
        const backupUrl = url.replace('cdn.jsdelivr.net', 'gcore.jsdelivr.net')
        if (backupUrl !== url) {
          audioRef.current.src = backupUrl
        }
      }

      audioRef.current.play().catch(err => {
        console.warn('自动播放被浏览器阻止:', err)
      })
    }

    tryPlayAudio(audioUrls[randomIndex])
  }

  if (!showPet) return null

  return (
    <>
      <canvas
        id='live2d'
        width='280'
        height='250'
        className='cursor-grab'
        onMouseDown={e => e.target.classList.add('cursor-grabbing')}
        onMouseUp={e => e.target.classList.remove('cursor-grabbing')}
      />
      <audio ref={audioRef} preload='auto' style={{ display: 'none' }} />
    </>
  )
}
