/**
 * APlayer 样式修复
 * 修复控制按钮位置和可见性问题
 */

/* 紧急修复：隐藏所有可能泄漏的音乐列表 */
body > ol,
body > ul,
main > ol,
main > ul,
#__next > ol,
#__next > ul {
  display: none !important;
}

/* 只允许在特定容器内显示列表 */
.aplayer ol,
.aplayer ul,
.post-content ol,
.post-content ul,
.notion-page ol,
.notion-page ul,
.article-content ol,
.article-content ul {
  display: block !important;
}

/* 主容器修复 */
.aplayer {
  position: relative !important;
  min-height: 120px !important;
  overflow: visible !important;
  background: #fff !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

/* 播放列表控制 */
.aplayer .aplayer-list {
  max-height: 300px !important;
  overflow-y: auto !important;
  background: #fff !important;
  border-top: 1px solid #e1e5e9 !important;
}

/* 默认隐藏播放列表，只有在展开时显示 */
.aplayer:not(.aplayer-withlist) .aplayer-list {
  display: none !important;
}

/* 防止歌曲列表直接显示在页面上 */
body > ol:not([class]),
body > ul:not([class]) {
  display: none !important;
}

/* 隐藏任何可能泄漏的音乐列表 */
body > ol[start],
body > ul[start] {
  display: none !important;
}

/* 只显示在播放器容器内的列表 */
.aplayer .aplayer-list ol,
.aplayer .aplayer-list ul {
  display: block !important;
  margin: 0 !important;
  padding: 0 !important;
  list-style: none !important;
}

/* 紧急修复：隐藏所有直接在body下的数字列表 */
body > ol {
  display: none !important;
}

/* 控制器容器修复 */
.aplayer .aplayer-controller {
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  height: 40px !important;
  padding: 8px 0 !important;
  margin-top: 12px !important;
  z-index: 10 !important;
}

/* 进度条区域 */
.aplayer .aplayer-controller .aplayer-bar-wrap {
  flex: 1 !important;
  margin: 0 12px !important;
  height: 4px !important;
  position: relative !important;
}

/* 右侧控制按钮组 */
.aplayer .aplayer-controller .aplayer-volume-wrap,
.aplayer .aplayer-controller .aplayer-mode,
.aplayer .aplayer-controller .aplayer-menu {
  position: relative !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 32px !important;
  height: 32px !important;
  margin: 0 2px !important;
  cursor: pointer !important;
  border-radius: 4px !important;
  background: transparent !important;
  transition: background 0.2s ease !important;
  z-index: 11 !important;
}

.aplayer .aplayer-controller .aplayer-volume-wrap:hover,
.aplayer .aplayer-controller .aplayer-mode:hover,
.aplayer .aplayer-controller .aplayer-menu:hover {
  background: rgba(0,0,0,0.05) !important;
}

/* 图标修复 */
.aplayer .aplayer-controller .aplayer-volume-wrap .aplayer-icon,
.aplayer .aplayer-controller .aplayer-mode .aplayer-icon,
.aplayer .aplayer-controller .aplayer-menu .aplayer-icon {
  width: 16px !important;
  height: 16px !important;
  fill: #666 !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 12 !important;
  pointer-events: none !important;
}

/* 音量控制面板位置修复 */
.aplayer .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap {
  position: absolute !important;
  bottom: 100% !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 4px !important;
  height: 60px !important;
  background: rgba(255,255,255,0.9) !important;
  border: 1px solid rgba(0,0,0,0.1) !important;
  border-radius: 2px !important;
  margin-bottom: 8px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  z-index: 100 !important;
}

.aplayer .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap .aplayer-volume-bar {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  background: #667eea !important;
  border-radius: 2px !important;
  transition: height 0.2s ease !important;
}

/* 菜单面板位置修复 */
.aplayer .aplayer-controller .aplayer-menu .aplayer-menu-panel {
  position: absolute !important;
  bottom: 100% !important;
  right: 0 !important;
  background: white !important;
  border: 1px solid rgba(0,0,0,0.1) !important;
  border-radius: 4px !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
  margin-bottom: 8px !important;
  min-width: 120px !important;
  z-index: 100 !important;
}

/* 播放模式按钮修复 */
.aplayer .aplayer-controller .aplayer-mode .aplayer-icon {
  transition: transform 0.2s ease !important;
}

.aplayer .aplayer-controller .aplayer-mode:active .aplayer-icon {
  transform: scale(0.9) !important;
}

/* 时间显示修复 */
.aplayer .aplayer-controller .aplayer-time {
  font-size: 12px !important;
  color: #666 !important;
  min-width: 40px !important;
  text-align: center !important;
  line-height: 1 !important;
  margin: 0 4px !important;
}

/* 播放按钮修复 */
.aplayer .aplayer-controller .aplayer-play {
  width: 36px !important;
  height: 36px !important;
  border-radius: 50% !important;
  background: #667eea !important;
  border: none !important;
  color: white !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  margin-right: 12px !important;
}

.aplayer .aplayer-controller .aplayer-play:hover {
  background: #5a67d8 !important;
  transform: scale(1.05) !important;
}

.aplayer .aplayer-controller .aplayer-play .aplayer-icon {
  width: 14px !important;
  height: 14px !important;
  fill: white !important;
}

/* 响应式修复 */
@media (max-width: 768px) {
  .aplayer .aplayer-controller {
    height: 36px !important;
    padding: 6px 0 !important;
  }
  
  .aplayer .aplayer-controller .aplayer-volume-wrap,
  .aplayer .aplayer-controller .aplayer-mode,
  .aplayer .aplayer-controller .aplayer-menu {
    width: 28px !important;
    height: 28px !important;
  }
  
  .aplayer .aplayer-controller .aplayer-play {
    width: 32px !important;
    height: 32px !important;
  }
  
  .aplayer .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap {
    height: 50px !important;
  }
}

/* 确保在所有主题下都可见 */
.dark .aplayer .aplayer-controller .aplayer-volume-wrap .aplayer-icon,
.dark .aplayer .aplayer-controller .aplayer-mode .aplayer-icon,
.dark .aplayer .aplayer-controller .aplayer-menu .aplayer-icon {
  fill: #ccc !important;
}

.dark .aplayer .aplayer-controller .aplayer-volume-wrap:hover,
.dark .aplayer .aplayer-controller .aplayer-mode:hover,
.dark .aplayer .aplayer-controller .aplayer-menu:hover {
  background: rgba(255,255,255,0.1) !important;
}

/* 修复层级问题 */
.aplayer * {
  box-sizing: border-box !important;
}

.aplayer .aplayer-controller * {
  position: relative !important;
  z-index: inherit !important;
}
