#!/usr/bin/env node

/**
 * 带重试机制的构建脚本
 * 专门处理网络连接问题导致的构建失败
 */

const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 开始带重试机制的构建...')

// 构建配置
const BUILD_CONFIG = {
  maxRetries: process.env.VERCEL ? 5 : 3, // Vercel环境增加重试次数
  retryDelay: process.env.VERCEL ? 3000 : 5000, // Vercel环境减少延迟
  timeout: process.env.VERCEL ? 900000 : 600000, // Vercel环境增加超时时间到15分钟
  networkErrorPatterns: [
    'ECONNRESET',
    'ETIMEDOUT',
    'ENOTFOUND',
    'ECONNREFUSED',
    'read ECONNRESET',
    'RequestError',
    'FetchError',
    'socket hang up',
    'network timeout'
  ]
}

/**
 * 检查是否是网络错误
 * @param {string} output - 构建输出
 * @returns {boolean} 是否是网络错误
 */
function isNetworkError(output) {
  return BUILD_CONFIG.networkErrorPatterns.some(pattern => 
    output.includes(pattern)
  )
}

/**
 * 运行构建命令
 * @returns {Promise<{success: boolean, output: string}>}
 */
function runBuild() {
  return new Promise((resolve) => {
    // 在Vercel环境中直接使用next build，避免递归调用
    const buildCommand = process.env.VERCEL ? 'next' : 'npm'
    const buildArgs = process.env.VERCEL ? ['build'] : ['run', 'build:original']

    console.log(`📦 执行构建命令: ${buildCommand} ${buildArgs.join(' ')}`)

    const buildProcess = spawn(buildCommand, buildArgs, {
      stdio: 'pipe',
      shell: true,
      cwd: process.cwd()
    })

    let output = ''
    let errorOutput = ''

    buildProcess.stdout.on('data', (data) => {
      const text = data.toString()
      output += text
      process.stdout.write(text) // 实时显示输出
    })

    buildProcess.stderr.on('data', (data) => {
      const text = data.toString()
      errorOutput += text
      process.stderr.write(text) // 实时显示错误
    })

    // 设置超时
    const timeout = setTimeout(() => {
      console.log('⏰ 构建超时，终止进程...')
      buildProcess.kill('SIGTERM')
      resolve({
        success: false,
        output: output + errorOutput + '\n构建超时'
      })
    }, BUILD_CONFIG.timeout)

    buildProcess.on('close', (code) => {
      clearTimeout(timeout)
      const fullOutput = output + errorOutput
      
      resolve({
        success: code === 0,
        output: fullOutput
      })
    })

    buildProcess.on('error', (error) => {
      clearTimeout(timeout)
      console.error('构建进程错误:', error)
      resolve({
        success: false,
        output: output + errorOutput + `\n进程错误: ${error.message}`
      })
    })
  })
}

/**
 * 主构建函数
 */
async function main() {
  let lastError = null
  
  for (let attempt = 1; attempt <= BUILD_CONFIG.maxRetries; attempt++) {
    console.log(`\n🔄 构建尝试 ${attempt}/${BUILD_CONFIG.maxRetries}`)
    
    const result = await runBuild()
    
    if (result.success) {
      console.log('✅ 构建成功！')
      
      // 生成构建报告
      const report = {
        success: true,
        attempts: attempt,
        timestamp: new Date().toISOString(),
        message: '构建成功完成'
      }
      
      fs.writeFileSync(
        path.join(process.cwd(), 'build-report.json'),
        JSON.stringify(report, null, 2)
      )
      
      process.exit(0)
    }
    
    lastError = result.output
    
    // 检查是否是网络错误
    const isNetworkIssue = isNetworkError(result.output)
    
    console.log(`❌ 构建失败 (尝试 ${attempt}/${BUILD_CONFIG.maxRetries})`)
    
    if (isNetworkIssue) {
      console.log('🌐 检测到网络错误，准备重试...')
    } else {
      console.log('⚠️ 非网络错误，可能需要手动修复')
    }
    
    // 如果不是最后一次尝试，等待后重试
    if (attempt < BUILD_CONFIG.maxRetries) {
      if (isNetworkIssue) {
        console.log(`⏳ 等待 ${BUILD_CONFIG.retryDelay / 1000} 秒后重试...`)
        await new Promise(resolve => setTimeout(resolve, BUILD_CONFIG.retryDelay))
      } else {
        // 非网络错误，减少重试次数
        console.log('💡 非网络错误，跳过剩余重试')
        break
      }
    }
  }
  
  // 所有尝试都失败了
  console.log('💥 所有构建尝试都失败了')
  
  // 生成失败报告
  const report = {
    success: false,
    attempts: BUILD_CONFIG.maxRetries,
    timestamp: new Date().toISOString(),
    lastError: lastError,
    suggestions: [
      '检查网络连接',
      '验证Notion API配置',
      '检查环境变量设置',
      '尝试手动构建: npm run build',
      '查看完整错误日志'
    ]
  }
  
  fs.writeFileSync(
    path.join(process.cwd(), 'build-report.json'),
    JSON.stringify(report, null, 2)
  )
  
  console.log('📊 构建报告已保存到 build-report.json')
  console.log('💡 建议检查网络连接和Notion API配置')
  
  process.exit(1)
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason)
  process.exit(1)
})

// 运行主函数
main().catch(error => {
  console.error('构建脚本执行失败:', error)
  process.exit(1)
})
