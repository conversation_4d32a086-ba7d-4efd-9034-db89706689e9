import { siteConfig } from '@/lib/config'
import { loadResourceWithFallback } from '@/lib/utils/resourceLoader'
import { useEffect, useRef, useState } from 'react'

/**
 * 音乐播放器
 * @returns
 */
const Player = () => {
  const [player, setPlayer] = useState()
  const ref = useRef(null)
  const lrcType = JSON.parse(siteConfig('MUSIC_PLAYER_LRC_TYPE'))
  const playerVisible = JSON.parse(siteConfig('MUSIC_PLAYER_VISIBLE'))
  const autoPlay = JSON.parse(siteConfig('MUSIC_PLAYER_AUTO_PLAY'))
  const meting = JSON.parse(siteConfig('MUSIC_PLAYER_METING'))
  const order = siteConfig('MUSIC_PLAYER_ORDER')
  const audio = siteConfig('MUSIC_PLAYER_AUDIO_LIST')

  const musicPlayerEnable = siteConfig('MUSIC_PLAYER')
  const musicPlayerCDN = siteConfig('MUSIC_PLAYER_CDN_URL')
  const musicMetingEnable = siteConfig('MUSIC_PLAYER_METING')
  const musicMetingCDNUrl = siteConfig(
    'MUSIC_PLAYER_METING_CDN_URL',
    'https://cdnjs.cloudflare.com/ajax/libs/meting/2.0.1/Meting.min.js'
  )

  const initMusicPlayer = async () => {
    if (!musicPlayerEnable || !ref.current) {
      return
    }

    try {
      // 首先清空容器并隐藏任何可能的泄漏内容
      ref.current.innerHTML = ''
      ref.current.style.display = 'none'

      // 首先加载CSS
      try {
        await loadResourceWithFallback('aplayerCSS', 'css', 10000)
      } catch (error) {
        // 静默处理CSS加载失败
      }

      // 然后加载JS
      await loadResourceWithFallback('aplayer', 'js', 12000)

      if (musicMetingEnable) {
        try {
          await loadResourceWithFallback('meting', 'js', 10000)
        } catch (error) {
          // 静默处理MetingJS加载失败
        }
      }

      // 确保容器存在且APlayer已加载
      if (!meting && window.APlayer && ref.current) {
        // 添加延迟确保DOM完全准备好
        setTimeout(() => {
          try {
            // 再次清空容器内容
            ref.current.innerHTML = ''

            // 验证音频数据
            if (!audio || !Array.isArray(audio) || audio.length === 0) {
              throw new Error('无效的音频数据')
            }

            const playerInstance = new window.APlayer({
              container: ref.current,
              fixed: true,
              lrcType: lrcType,
              autoplay: autoPlay,
              order: order,
              audio: audio,
              listFolded: true, // 默认折叠播放列表
              listMaxHeight: '300px', // 限制列表高度
              volume: 0.7,
              mutex: true, // 阻止多个播放器同时播放
              preload: 'auto'
            })

            // 显示容器
            ref.current.style.display = 'block'
            setPlayer(playerInstance)
          } catch (error) {
            // 如果初始化失败，使用备用方案
            if (window.APlayerFallback && ref.current) {
              ref.current.innerHTML = ''
              new window.APlayerFallback({
                container: ref.current,
                audio: audio
              })
              ref.current.style.display = 'block'
            }
          }
        }, 100)
      }
    } catch (error) {
      // 静默处理整体初始化失败，使用备用方案
      if (window.APlayerFallback && ref.current) {
        ref.current.innerHTML = ''
        new window.APlayerFallback({
          container: ref.current,
          audio: audio
        })
        ref.current.style.display = 'block'
      }
    }
  }

  useEffect(() => {
    if (musicPlayerEnable) {
      initMusicPlayer()
    }

    return () => {
      setPlayer(undefined)
    }
  }, [])



  return (
    <div className={playerVisible ? 'visible' : 'invisible'}>
      {meting ? (
        <meting-js
          fixed='true'
          type='playlist'
          preload='auto'
          api={siteConfig(
            'MUSIC_PLAYER_METING_API',
            'https://api.i-meto.com/meting/api?server=:server&type=:type&id=:id&r=:r'
          )}
          autoplay={autoPlay}
          order={siteConfig('MUSIC_PLAYER_ORDER')}
          server={siteConfig('MUSIC_PLAYER_METING_SERVER')}
          id={siteConfig('MUSIC_PLAYER_METING_ID')}
        />
      ) : (
        <div ref={ref} data-player={player} />
      )}
    </div>
  )
}

export default Player
